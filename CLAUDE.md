# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Gantt chart project management system for construction project scheduling. The application allows users to create, edit, and visualize project timelines with interactive甘特图 (Gantt chart) functionality.

## Key Technologies

- **Frontend**: HTML, CSS, JavaScript
- **Backend**: Node.js with built-in HTTP server
- **External Libraries**: 
  - SheetJS (xlsx) for Excel import/export functionality
  - Puppeteer for PDF generation (declared in package.json)

## Project Structure

- `index.html` - Main HTML file containing the UI structure
- `script.js` - Client-side JavaScript with all application logic
- `styles.css` - Styling for the entire application
- `server.js` - Simple Node.js HTTP server to serve the application
- `package.json` - Project metadata and dependencies

## Development Commands

```bash
# Start the development server
npm run dev

# Start the production server
npm start

# Both commands run: node server.js
```

## Architecture Overview

### Frontend Architecture

The application follows a component-based approach within a single page:

1. **Toolbar** - Contains all action buttons (add task, edit, delete, import/export, etc.)
2. **Unified Table View** - Combines task information and Gantt chart visualization in a single scrollable area
3. **Modal Dialogs** - Various forms for task creation, group management, and settings
4. **Interactive Gantt Chart** - Drag-and-drop functionality for adjusting task timelines

### Backend Architecture

The backend is a simple static file server that serves the frontend files. All application logic runs in the browser.

### Data Flow

1. Tasks are stored in memory as JavaScript objects
2. UI updates are handled through DOM manipulation
3. Task persistence is achieved through import/export functionality (Excel files)
4. Changes are immediately reflected in both the task table and Gantt visualization

## Key Features

- Task creation and management with grouping capabilities
- Interactive Gantt chart with drag-and-drop scheduling
- Excel import/export functionality
- Customizable display settings (column visibility, task bar styling)
- Batch operations for multiple task editing
- Responsive design for different screen sizes

## Common Development Tasks

### Adding a New Feature

1. Identify the relevant section in `index.html` for UI elements
2. Add event handlers in `script.js` 
3. Update styles in `styles.css` if needed
4. Test with `npm run dev`

### Modifying Task Data Structure

1. Update the task object structure in `script.js`
2. Modify form fields in the task modal in `index.html`
3. Update table rendering logic in `script.js`
4. Adjust Gantt chart rendering to accommodate new fields

### Styling Changes

1. Most styles are in `styles.css`
2. Some inline styles exist in `index.html` for dynamic elements
3. CSS variables are used for consistent sizing (row heights, fonts, etc.)

## Additional Technical Details

### Task Data Structure

Tasks are stored in localStorage as JavaScript objects with the following structure:
- `id`: Unique identifier for the task
- `group`: Group/category the task belongs to
- `number`: Task number/ID
- `name`: Task name/description
- `duration`: Duration in days
- `startDate`: Start date in YYYY-MM-DD format
- `endDate`: End date in YYYY-MM-DD format
- `color`: Color for the Gantt chart bar

### Gantt Chart Implementation

The Gantt chart is implemented with:
- Fixed row height of 45px (controlled by CSS variables)
- Dynamic time ruler generation based on date range
- Draggable task bars for timeline adjustment
- Support for custom step sizes (days per grid unit)

### Border and Styling Settings

The application supports customizable border settings stored in localStorage:
- Border color and width
- Task bar height
- Transparency options

### Excel Import/Export

Excel functionality is implemented using the SheetJS library:
- Export: Converts task data to Excel format
- Import: Reads Excel files and populates task data